<template>
  <div>
    <el-card>
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <div>
          <el-input placeholder="搜索姓名" v-model="search" style="width: 200px; margin-right: 8px;" />
        </div>
        <el-button type="primary" @click="openAdd">添加用户</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%" :border="true" size="small">
        <el-table-column prop="user_id" label="用户ID" width="100" />
        <el-table-column prop="account" label="账号" width="120" />
        <el-table-column label="密码" width="120">
          <template #default>
            <span>******</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column label="角色" width="120">
          <template #default="scope">
            <span>{{ getRoleName(scope.row.role_id) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部门" width="120">
          <template #default="scope">
            <span>{{ getDepartmentName(scope.row.department_id) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <el-button type="text" size="small" @click="openEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" danger @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        layout="prev, pager, next, jumper"
        :total="total"
        v-model:current-page="page"
        v-model:page-size="pageSize"
        style="margin-top: 16px; text-align: right;"
      />
    </el-card>
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑用户' : '新增用户'" width="400px">
      <el-form :model="form" label-width="90px">
        <el-form-item label="用户ID" v-if="isEdit">
          <el-input v-model="form.user_id" disabled />
        </el-form-item>
        <el-form-item label="账号">
          <el-input v-model="form.account" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="form.password" show-password />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="角色ID">
          <el-select v-model="form.role_id" placeholder="请选择角色" style="width: 100%">
            <el-option
              v-for="role in roles"
              :key="role.role_id"
              :label="role.role_name"
              :value="role.role_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="form.department_id" placeholder="请选择部门" style="width: 100%">
            <el-option
              v-for="dept in departments"
              :key="dept.department_id"
              :label="dept.department_name"
              :value="dept.department_id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="formLoading" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

const search = ref('')
const tableData = ref([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const departments = ref([])
const roles = ref([])

const fetchUsers = async () => {
  const res = await axios.get('/api/users', {
    params: {
      search: search.value,
      page: page.value,
      pageSize: pageSize.value
    }
  })
  tableData.value = res.data.list
  total.value = res.data.total
}

const fetchDepartments = async () => {
  const res = await axios.get('/api/departments/all')
  departments.value = res.data
}

const fetchRoles = async () => {
  const res = await axios.get('/api/roles/all')
  roles.value = res.data
}

onMounted(() => {
  fetchUsers()
  fetchDepartments()
  fetchRoles()
})
watch([search, page, pageSize], fetchUsers)

// 新增/编辑弹窗
const dialogVisible = ref(false)
const isEdit = ref(false)
const form = ref({ user_id: '', account: '', password: '', name: '', role_id: '', department_id: '' })
const formLoading = ref(false)

const openAdd = () => {
  isEdit.value = false
  form.value = { user_id: '', account: '', password: '', name: '', role_id: '', department_id: '' }
  dialogVisible.value = true
}

const openEdit = (row) => {
  isEdit.value = true
  form.value = { ...row }
  dialogVisible.value = true
}

const handleSubmit = async () => {
  // 密码强度验证
  if (form.value.password && form.value.password.length < 6) {
    ElMessage.error('密码长度至少6位')
    return
  }
  formLoading.value = true
  try {
    if (isEdit.value) {
      await axios.put(`/api/users/${form.value.user_id}`, {
        account: form.value.account,
        password: form.value.password,
        name: form.value.name,
        role_id: form.value.role_id,
        department_id: form.value.department_id
      })
      ElMessage.success('修改成功')
    } else {
      await axios.post('/api/users', {
        account: form.value.account,
        password: form.value.password,
        name: form.value.name,
        role_id: form.value.role_id,
        department_id: form.value.department_id
      })
      ElMessage.success('添加成功')
    }
    dialogVisible.value = false
    fetchUsers()
  } catch (e) {
    ElMessage.error('操作失败')
  } finally {
    formLoading.value = false
  }
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该用户吗？', '提示', { type: 'warning' })
    .then(async () => {
      await axios.delete(`/api/users/${row.user_id}`)
      ElMessage.success('删除成功')
      fetchUsers()
    })
    .catch(() => {})
}

const getRoleName = (role_id) => {
  const role = roles.value.find(r => r.role_id === role_id)
  return role ? role.role_name : ''
}
const getDepartmentName = (department_id) => {
  const dept = departments.value.find(d => d.department_id === department_id)
  return dept ? dept.department_name : ''
}
</script> 