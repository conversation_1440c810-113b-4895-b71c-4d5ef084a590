<template>
  <div class="login-root">
    <div class="login-left">
      <div class="welcome-content">
        <h4>稻盛科技AI数据管理平台</h4>
      </div>
    </div>
    <div class="login-right">
      <div class="login-card">
        <h2>登 录</h2>
        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <label>账号</label>
            <input v-model="username" type="text" placeholder="请输入账号" required />
          </div>
          <div class="form-group">
            <label>密码</label>
            <input v-model="password" type="password" placeholder="请输入密码" required />
          </div>
          <button type="submit" class="login-btn">登录</button>
          <div v-if="error" class="error-message">{{ error }}</div>
        </form>
        <div class="register-link">
          <span>没有账号？</span>
          <a href="javascript:void(0)" @click="registerDialogVisible = true">注册</a>
        </div>
      </div>
    </div>
    <el-dialog v-model="registerDialogVisible" title="注册账号" width="350px" :close-on-click-modal="false" :close-on-press-escape="true" class="register-dialog">
      <form @submit.prevent="handleRegister" class="register-form">
        <div class="form-group">
          <label>账号</label>
          <input v-model="registerForm.account" type="text" placeholder="请输入账号" required />
        </div>
        <div class="form-group">
          <label>密码</label>
          <input v-model="registerForm.password" type="password" placeholder="请输入密码" required />
        </div>
        <div class="form-group">
          <label>姓名</label>
          <input v-model="registerForm.name" type="text" placeholder="请输入姓名" required />
        </div>
        <button type="submit" class="login-btn" style="width:100%;margin-top:12px;">注册</button>
        <div v-if="registerError" class="error-message">{{ registerError }}</div>
      </form>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { userAPI } from '@/api/index.js'
import { setCurrentUser, setLoginState } from '@/utils/user.js'

const router = useRouter()
const username = ref('')
const password = ref('')
const error = ref('')

const registerDialogVisible = ref(false)
const registerForm = ref({ account: '', password: '', name: '' })
const registerError = ref('')

const handleLogin = async () => {
  if (!username.value || !password.value) {
    error.value = '请输入账号和密码'
    return
  }
  try {
    const response = await userAPI.login({
      account: username.value,
      password: password.value
    })
    console.log('login response:', response)
    if (response.user_id) {
      const userInfo = {
        userId: Number(response.user_id),
        account: response.account,
        name: response.name,
        roleId: response.role_id,
        departmentId: Number(response.department_id),
        token: response.token
      }
      if (userInfo.userId && userInfo.departmentId) {
        setCurrentUser(userInfo)
        setLoginState(true)
        ElMessage.success(`欢迎回来，${userInfo.name || userInfo.account}！`)
        router.replace('/ai')
      } else {
        error.value = '用户信息异常，请联系管理员'
        return
      }
    } else {
      error.value = response.message || '登录失败，请检查账号密码'
    }
  } catch (err) {
    error.value = err.response?.data?.message || '登录失败，请检查账号密码'
  }
}

const handleRegister = async () => {
  if (!registerForm.value.account || !registerForm.value.password || !registerForm.value.name) {
    registerError.value = '请填写完整信息'
    return
  }
  if (registerForm.value.password.length < 6) {
    registerError.value = '密码长度至少6位'
    return
  }
  try {
    const res = await userAPI.register({
      account: registerForm.value.account,
      password: registerForm.value.password,
      name: registerForm.value.name
    })
    console.log('register response:', res)
    if (res.user_id) {
      // 注册成功后自动登录
      const loginRes = await userAPI.login({
        account: registerForm.value.account,
        password: registerForm.value.password
      })
      console.log('auto-login response:', loginRes)
      if (loginRes.user_id) {
        const userInfo = {
          userId: Number(loginRes.user_id),
          account: loginRes.account,
          name: loginRes.name,
          roleId: loginRes.role_id,
          departmentId: Number(loginRes.department_id),
          token: loginRes.token
        }
        if (userInfo.userId && userInfo.departmentId) {
          setCurrentUser(userInfo)
          setLoginState(true)
          ElMessage.success(`注册并登录成功，欢迎${userInfo.name || userInfo.account}！`)
          registerDialogVisible.value = false
          registerForm.value = { account: '', password: '', name: '' }
          registerError.value = ''
          router.replace('/ai')
        } else {
          registerError.value = '用户信息异常，请联系管理员'
          return
        }
      } else {
        registerError.value = loginRes.message || '自动登录失败'
      }
    } else {
      registerError.value = res.message || '注册失败'
    }
  } catch (err) {
    registerError.value = err.response?.data?.message || '注册失败'
  }
}
</script>

<style scoped>
.login-root {
  display: flex;
  min-height: 100vh;
  width: 100vw;
  background: #f5f7fa;
}
.login-left {
  flex: 1.2;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
}
.welcome-content {
  color: #fff;
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
}
.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(44,62,80,0.10);
  padding: 40px 32px 24px 32px;
  width: 100%;
  max-width: 350px;
  min-width: 260px;
  box-sizing: border-box;
  text-align: center;
  position: relative;
}
.login-title {
  font-size: 36px;
  font-weight: bold;
  letter-spacing: 16px;
  color: #222;
  margin-bottom: 32px;
  text-align: center;
}
.login-form {
  width: 100%;
}
.form-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  margin-bottom: 16px;
}
.form-group label {
  font-size: 1rem;
  color: #333;
  font-weight: 500;
}
.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1.5px solid #e1e5e9;
  border-radius: 6px;
  font-size: 16px;
  background: #f9f9f9;
  transition: border-color 0.2s;
}
.form-group input:focus {
  outline: none;
  border-color: #2196f3;
}
.login-btn {
  background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
  color: white;
  padding: 12px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 8px;
  transition: background 0.2s;
}
.login-btn:hover {
  background: linear-gradient(135deg, #1976d2 0%, #00bcd4 100%);
}
.error-message {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 8px;
  text-align: left;
}
.register-link {
  margin-top: 18px;
  font-size: 15px;
  color: #888;
  text-align: center;
}
.register-link a {
  color: #2196f3;
  margin-left: 4px;
  text-decoration: underline;
  cursor: pointer;
}
.register-dialog ::v-deep .el-dialog__body {
  padding-top: 0;
  padding-bottom: 24px;
}
.register-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style> 