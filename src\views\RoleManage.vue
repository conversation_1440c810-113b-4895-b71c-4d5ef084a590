<template>
  <div>
    <el-card>
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <div>
          <el-input placeholder="搜索角色名" v-model="search" style="width: 200px; margin-right: 8px;" />
        </div>
        <el-button type="primary" @click="openAdd">添加角色</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%" :border="true" size="small">
        <el-table-column prop="role_id" label="角色ID" width="100" />
        <el-table-column prop="role_name" label="角色名" width="180" />
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <el-button type="text" size="small" @click="openEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" danger @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑角色' : '新增角色'" width="400px">
      <el-form :model="form" label-width="90px">
        <el-form-item label="角色ID" v-if="isEdit">
          <el-input v-model="form.role_id" disabled />
        </el-form-item>
        <el-form-item label="角色名">
          <el-input v-model="form.role_name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="formLoading" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

const search = ref('')
const tableData = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const form = ref({ role_id: '', role_name: '' })
const formLoading = ref(false)

const fetchRoles = async () => {
  const res = await axios.get('/api/roles', { params: { search: search.value } })
  tableData.value = res.data.list || res.data
}

onMounted(fetchRoles)

const openAdd = () => {
  isEdit.value = false
  form.value = { role_id: '', role_name: '' }
  dialogVisible.value = true
}

const openEdit = (row) => {
  isEdit.value = true
  form.value = { ...row }
  dialogVisible.value = true
}

const handleSubmit = async () => {
  formLoading.value = true
  try {
    if (isEdit.value) {
      await axios.put(`/api/roles/${form.value.role_id}`, { role_name: form.value.role_name })
      ElMessage.success('修改成功')
    } else {
      await axios.post('/api/roles', { role_name: form.value.role_name })
      ElMessage.success('添加成功')
    }
    dialogVisible.value = false
    fetchRoles()
  } catch (e) {
    ElMessage.error('操作失败')
  } finally {
    formLoading.value = false
  }
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该角色吗？', '提示', { type: 'warning' })
    .then(async () => {
      await axios.delete(`/api/roles/${row.role_id}`)
      ElMessage.success('删除成功')
      fetchRoles()
    })
    .catch(() => {})
}
</script> 