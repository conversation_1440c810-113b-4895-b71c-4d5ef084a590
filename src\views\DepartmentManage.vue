<template>
  <div>
    <el-card>
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <div>
          <el-input placeholder="搜索部门名" v-model="search" style="width: 200px; margin-right: 8px;" />
        </div>
        <el-button type="primary" @click="openAdd">添加部门</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%" :border="true" size="small">
        <el-table-column prop="department_id" label="部门ID" width="100" />
        <el-table-column prop="department_name" label="部门名" width="180" />
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <el-button type="text" size="small" @click="openEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" danger @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑部门' : '新增部门'" width="400px">
      <el-form :model="form" label-width="90px">
        <el-form-item label="部门ID" v-if="isEdit">
          <el-input v-model="form.department_id" disabled />
        </el-form-item>
        <el-form-item label="部门名">
          <el-input v-model="form.department_name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="formLoading" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

const search = ref('')
const tableData = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const form = ref({ department_id: '', department_name: '' })
const formLoading = ref(false)

const fetchDepartments = async () => {
  const res = await axios.get('/api/departments', { params: { search: search.value } })
  tableData.value = res.data.list || res.data
}

onMounted(fetchDepartments)

const openAdd = () => {
  isEdit.value = false
  form.value = { department_id: '', department_name: '' }
  dialogVisible.value = true
}

const openEdit = (row) => {
  isEdit.value = true
  form.value = { ...row }
  dialogVisible.value = true
}

const handleSubmit = async () => {
  formLoading.value = true
  try {
    if (isEdit.value) {
      await axios.put(`/api/departments/${form.value.department_id}`, { department_name: form.value.department_name })
      ElMessage.success('修改成功')
    } else {
      await axios.post('/api/departments', { department_name: form.value.department_name })
      ElMessage.success('添加成功')
    }
    dialogVisible.value = false
    fetchDepartments()
  } catch (e) {
    ElMessage.error('操作失败')
  } finally {
    formLoading.value = false
  }
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该部门吗？', '提示', { type: 'warning' })
    .then(async () => {
      await axios.delete(`/api/departments/${row.department_id}`)
      ElMessage.success('删除成功')
      fetchDepartments()
    })
    .catch(() => {})
}
</script> 