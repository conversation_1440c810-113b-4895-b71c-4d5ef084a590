<template>
  <div class="topbar">
    <div class="title">小海AI管理平台</div>
    <div class="actions">
      <el-dropdown @command="handleCommand">
        <span class="user-info">
          <el-avatar :size="32" class="user-avatar">
            {{ userAvatarText }}
          </el-avatar>
          <span class="user-name">{{ userDisplayName }}</span>
          <el-icon><ArrowDown /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item disabled>
              <div class="user-detail">
               
                <div class="user-detail-account">{{ user.value?.account || '' }}</div>
                <div class="user-detail-role">角色：{{ userRoleName }}</div>
              </div>
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { getUserDisplayName, getUserAvatarText, getCurrentUser, clearCurrentUser } from '@/utils/user.js'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowDown, SwitchButton } from '@element-plus/icons-vue'

const router = useRouter()

const user = computed(() => getCurrentUser())
const userDisplayName = computed(() => user.value ? user.value.name || user.value.account || '用户' : '用户')
const userAvatarText = computed(() => user.value ? (user.value.name ? user.value.name.charAt(0) : (user.value.account ? user.value.account.charAt(0) : 'U')) : 'U')

// 角色ID与名称映射
const roleNameMap = {
  1: '管理员',
  2: '普通用户',
  3: '访客',
  // 可根据实际角色ID扩展
}
const userRoleName = computed(() => user.value ? (roleNameMap[user.value.roleId] || '未知角色') : '未知角色')

function handleCommand(command) {
  if (command === 'logout') {
    clearCurrentUser()
    ElMessage.success('已退出登录')
    router.replace('/login')
  }
}
</script>

<style scoped>
.topbar {
  height: 80px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  box-shadow: 0 2px 8px rgba(44,62,80,0.04);
  z-index: 10;
}
.title {
  font-size: 22px;
  font-weight: bold;
  color: #2196f3;
}
.actions {
  display: flex;
  align-items: center;
}
.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.user-avatar {
  margin-right: 8px;
  background: #2196f3;
  color: #fff;
}
.user-name {
  margin-right: 4px;
  font-weight: 500;
}
.user-detail {
  padding: 8px 0;
}
.user-detail-name {
  font-weight: bold;
}
.user-detail-account {
  font-size: 13px;
  color: #888;
}
</style> 