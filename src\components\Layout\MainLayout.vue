<template>
  <div class="main-layout">
    <TopBar />
    <div class="main-content">
      <SideMenu />
      <div class="page-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import TopBar from './TopBar.vue'
import SideMenu from './SideMenu.vue'
</script>

<style scoped>
.main-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
}
.main-content {
  display: flex;
  flex: 1;
  background: #f5f7fa;
}
.page-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}
</style> 