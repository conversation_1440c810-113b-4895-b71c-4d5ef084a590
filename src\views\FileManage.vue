<template>
  <div>
    <el-button type="primary" @click="uploadDialogVisible = true">上传文件</el-button>
    <el-dialog v-model="uploadDialogVisible" title="上传文件" width="400px">
      <el-form>
        <el-form-item label="文件分类">
          <el-select v-model="uploadData.file_category" placeholder="请选择分类" style="width: 100%">
            <el-option label="个人文件" value="个人文件" />
            <el-option label="部门文件" value="部门文件" />
            <el-option label="公共文件" value="公共文件" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择文件">
          <el-upload
            ref="uploadRef"
            :action="`${apiBaseUrl}/api/files/upload`"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :show-file-list="true"
            :data="uploadData"
            :auto-upload="false"
          >
            <el-button type="primary">选择文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitUpload">上传</el-button>
      </template>
    </el-dialog>
    <el-row :gutter="24" style="margin-top: 32px;">
      <el-col :span="6" v-for="file in tableData" :key="file.file_id">
        <el-card class="file-card">
          <div class="file-thumb-wrapper">
            <img
              v-if="file.fileType && ['png','jpg','jpeg','gif','bmp','webp'].includes(file.fileType.toLowerCase())"
              :src="`/${file.filePath}`"
              alt="缩略图"
              class="file-thumb"
              @click="handlePreview(file)"
              style="cursor:pointer;"
            >
            <div v-else class="file-icon" @click="handlePreview(file)" style="cursor:pointer;">
              <el-icon><i class="el-icon-document"></i></el-icon>
            </div>
          </div>
          <div class="file-meta">
            <div class="file-name" :title="file.originalFileName || file.filePath" @click="handlePreview(file)" style="cursor:pointer;">
              {{ (file.originalFileName || file.filePath).length > 18 ? (file.originalFileName || file.filePath).slice(0, 16) + '...' : (file.originalFileName || file.filePath) }}
            </div>
            <div class="file-type">类型：{{ file.fileType }}</div>
            <div class="file-category">分类：{{ file.fileCategory }}</div>
            <div class="file-time">上传：{{ file.uploadTime }}</div>
            <div class="file-uploader">上传者：{{ file.uploaderName }}</div>
          </div>
          <el-button type="danger" size="small" class="file-delete-btn" @click="handleDelete(file)">删除</el-button>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog v-model="previewDialogVisible" :title="previewFileTitle" width="600px" top="5vh">
      <template v-if="!imgError">
        <img
          :src="`/${previewFile.filePath}`"
          alt="预览"
          style="max-width:100%;max-height:60vh;display:block;margin:auto;"
          @error="onImgError"
          @load="onImgLoad"
        />
      </template>
      <template v-else>
        <div style="color:#f56c6c;text-align:center;margin-top:12px;">图片加载失败，请检查文件是否存在或格式是否正确</div>
      </template>
      <div v-if="!isImageFile(previewFile)" style="text-align:center;padding:32px 0;">
        <el-icon style="font-size:48px;color:#2196f3;"><i class="el-icon-document"></i></el-icon>
        <div style="margin:16px 0 8px 0;font-size:18px;font-weight:500;">{{ previewFile.originalFileName || previewFile.filePath }}</div>
        <el-button type="primary" :href="`/${previewFile.filePath}`" target="_blank">下载/浏览文件</el-button>
        <div style="color:#888;font-size:13px;margin-top:8px;">暂不支持在线预览此类型文件，可下载后用本地软件打开。</div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentUser } from '@/utils/user.js'

const user = getCurrentUser()
if (!user) {
  ElMessage.error('用户信息缺失，请重新登录')
  window.location.href = '/login'
}

const apiBaseUrl = ''
const tableData = ref([])
const uploadHeaders = {}
const uploadDialogVisible = ref(false)
const uploadRef = ref(null)
const uploadData = ref({
  user_id: null,
  file_category: '',
  department_id: null,
  uploader_name: ''
})

const previewDialogVisible = ref(false)
const previewFile = ref({})
const previewFileTitle = ref('文件预览')
const imgError = ref(false)

function ensureUserInfo() {
  const user = getCurrentUser()
  if (!user || !user.userId || !user.departmentId) {
    ElMessage.error('用户信息缺失，请重新登录')
    window.location.href = '/login'
    throw new Error('用户信息缺失')
  }
  return user
}

function isImageFile(file) {
  if (!file || !file.fileType) return false
  // 支持更多图片后缀，忽略大小写
  const ext = String(file.fileType).toLowerCase()
  return [
    'png','jpg','jpeg','gif','bmp','webp','svg','ico','jfif','tiff','apng','avif'
  ].includes(ext)
}

function handlePreview(file) {
  previewFile.value = file
  previewFileTitle.value = (file.originalFileName || file.filePath) + ' 预览'
  imgError.value = false
  previewDialogVisible.value = true
}

function onImgError() { imgError.value = true }
function onImgLoad() { imgError.value = false }

const fetchFiles = async () => {
  const user = ensureUserInfo()
  const res = await axios.get('/api/files', {
    params: {
      userId: user.userId,
      departmentId: user.departmentId
    }
  })
  const rawList = res.data.list || res.data
  tableData.value = rawList.map(item => ({
    ...item,
    file_id: item.file_id || item.id || item.fileId || item.fileID || item._id
  }))
}

onMounted(fetchFiles)

const handleUploadSuccess = () => {
  ElMessage.success('上传成功')
  uploadDialogVisible.value = false
  fetchFiles()
}

const submitUpload = () => {
  const user = ensureUserInfo()
  if (!uploadData.value.file_category) {
    ElMessage.warning('请选择文件分类')
    return
  }
  uploadData.value.user_id = user.userId
  uploadData.value.department_id = user.departmentId
  uploadData.value.uploader_name = user.name
  uploadRef.value.submit()
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该文件吗？', '提示', { type: 'warning' })
    .then(async () => {
      await axios.delete(`/api/files/${row.file_id}`)
      ElMessage.success('删除成功')
      fetchFiles()
    })
    .catch(() => {})
}
</script>

<style scoped>
.file-card {
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(33,150,243,0.08), 0 1.5px 4px 0 rgba(44,62,80,0.04);
  transition: box-shadow 0.2s;
  padding: 18px 18px 12px 18px;
  background: linear-gradient(135deg, #f5f7fa 60%, #e3f0ff 100%);
  position: relative;
  min-height: 320px;
  max-height: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  box-sizing: border-box;
}
.file-card:hover {
  box-shadow: 0 8px 32px 0 rgba(33,150,243,0.18), 0 3px 8px 0 rgba(44,62,80,0.08);
}
.file-thumb-wrapper {
  width: 100%;
  height: 130px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
  /* background: #fff; */
  /* border-radius: 8px; */
  /* overflow: hidden; */
}
.file-thumb {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(33,150,243,0.10);
  background: #fff;
  display: block;
}
.file-icon {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  background: #e3f0ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  color: #2196f3;
}
.file-meta {
  width: 100%;
  text-align: center;
  margin-bottom: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.file-name {
  font-weight: 600;
  font-size: 16px;
  color: #222;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
}
.file-type, .file-category, .file-time, .file-uploader {
  font-size: 13px;
  color: #666;
  margin-bottom: 2px;
}
.file-delete-btn {
  position: absolute;
  right: 12px;
  bottom: 12px;
  font-size: 13px;
  border-radius: 8px;
  padding: 2px 12px;
}
</style> 