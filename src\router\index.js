import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '../components/Layout/MainLayout.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/ai',
    component: MainLayout,
    children: [
      {
        path: '',
        component: () => import('../views/Ai.vue'),
        name: '小海AI',
        meta: { requiresAuth: true }
      }
    ]
  },
  {
    path: '/data',
    component: MainLayout,
    children: [
      {
        path: '',
        component: () => import('../views/DataManage.vue'),
        name: '数据管理',
        meta: { requiresAuth: true }
      }
    ]
  },
  {
    path: '/file',
    component: MainLayout,
    children: [
      {
        path: '',
        component: () => import('../views/FileManage.vue'),
        name: '文件管理',
        meta: { requiresAuth: true }
      }
    ]
  },
  {
    path: '/user',
    component: MainLayout,
    children: [
      {
        path: '',
        component: () => import('../views/UserManage.vue'),
        name: '用户管理',
        meta: { requiresAuth: true }
      }
    ]
  },
  {
    path: '/department',
    component: MainLayout,
    children: [
      {
        path: '',
        component: () => import('../views/DepartmentManage.vue'),
        name: '部门管理',
        meta: { requiresAuth: true }
      }
    ]
  },
  {
    path: '/role',
    component: MainLayout,
    children: [
      {
        path: '',
        component: () => import('../views/RoleManage.vue'),
        name: '角色管理',
        meta: { requiresAuth: true }
      }
    ]
  },
  // 404 页面
  {
    path: '/:catchAll(.*)',
    redirect: '/login'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router 