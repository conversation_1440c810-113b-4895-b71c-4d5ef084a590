# 路由和登录系统改进说明

## 改进内容

### 1. 自动登录检查
- **功能**: 应用启动时自动检查登录状态
- **实现**: 在 `main.js` 中添加了 `initApp()` 函数
- **效果**: 避免每次刷新页面都需要重新登录

### 2. 开发环境默认登录
- **功能**: 开发环境下自动设置默认登录状态
- **配置**: 在 `src/config/dev.js` 中配置
- **效果**: 开发时无需手动登录，直接进入系统

### 3. 登录状态有效期管理
- **功能**: 登录状态24小时后自动过期
- **实现**: 在 `utils/user.js` 中的 `isLoginValid()` 函数
- **效果**: 提高安全性，避免长期未使用的登录状态

### 4. 改进的路由守卫
- **功能**: 更智能的路由权限控制
- **实现**: 在 `router/index.js` 中更新了路由守卫
- **效果**: 更好的用户体验和安全性

### 5. 用户友好的登出功能
- **功能**: 登出时显示确认对话框
- **实现**: 在 `TopBar.vue` 中使用 `ElMessageBox`
- **效果**: 避免误操作，提供更好的用户体验

## 配置文件

### `src/config/dev.js`
```javascript
export const devConfig = {
  // 是否启用默认登录
  enableDefaultLogin: true,
  
  // 默认用户信息
  defaultUser: {
    userId: 1,
    account: 'admin',
    name: '管理员',
    roleId: 1,
    departmentId: 1,
    token: 'dev-token-' + Date.now()
  },
  
  // 登录过期时间（小时）
  loginExpireHours: 24,
  
  // 是否显示调试信息
  showDebugInfo: true
}
```

## 使用方法

### 开发环境
1. 启动应用后会自动设置默认登录状态
2. 无需手动登录，直接进入系统
3. 可以在控制台看到调试信息

### 生产环境
1. 需要手动登录
2. 登录状态24小时后自动过期
3. 刷新页面会保持登录状态

### 自定义配置
1. 修改 `src/config/dev.js` 中的配置
2. 调整登录过期时间
3. 更改默认用户信息

## 新增的工具函数

### `utils/user.js`
- `isLoginValid()`: 检查登录状态是否有效
- `setLoginState()`: 设置完整的登录状态
- `setDevDefaultLogin()`: 设置开发环境默认登录
- `checkDevDefaultLogin()`: 检查并设置开发环境默认登录

### `config/dev.js`
- `devConfig`: 开发环境配置对象
- `devUtils`: 开发环境工具函数

## 路由结构

```
/                    -> 重定向到 /user
/login              -> 登录页面
/user               -> 用户管理（需要登录）
/department         -> 部门管理（需要登录）
/role               -> 角色管理（需要登录）
/permission         -> 权限管理（需要登录）
/file               -> 文件管理（需要登录）
/data               -> 数据管理（需要登录）
/ai                 -> 小海AI（需要登录）
/:pathMatch(.*)*    -> 404页面，重定向到 /user
```

## 安全特性

1. **登录状态验证**: 每次路由跳转都会验证登录状态
2. **自动过期**: 登录状态24小时后自动清除
3. **开发环境隔离**: 默认登录只在开发环境生效
4. **用户确认**: 登出时需要用户确认

## 调试信息

在开发环境下，控制台会显示以下调试信息：
- 登录状态设置
- 登录过期检查
- 开发环境默认登录设置
- 路由跳转信息 