// 用户信息管理工具函数

/** 获取当前用户信息 */
export function getCurrentUser() {
  try {
    const userInfo = localStorage.getItem('userInfo')
    if (!userInfo) return null
    const user = JSON.parse(userInfo)
    // 类型保护
    if (!user.userId || !user.departmentId) return null
    user.userId = Number(user.userId)
    user.departmentId = Number(user.departmentId)
    return user
  } catch (error) {
    return null
  }
}

/** 设置用户信息 */
export function setCurrentUser(userInfo) {
  localStorage.setItem('userInfo', JSON.stringify(userInfo))
}

/** 清除用户信息 */
export function clearCurrentUser() {
  localStorage.removeItem('userInfo')
  localStorage.removeItem('isLoggedIn')
  localStorage.removeItem('token')
  localStorage.removeItem('loginTime')
}

/** 设置登录状态 */
export function setLoginState(isLoggedIn) {
  localStorage.setItem('isLoggedIn', isLoggedIn ? 'true' : 'false')
}

/** 获取用户显示名称 */
export function getUserDisplayName() {
  const user = getCurrentUser()
  if (!user) return '用户'
  return user.name || user.account || '用户'
}

/** 获取用户头像文字 */
export function getUserAvatarText() {
  const user = getCurrentUser()
  if (!user) return 'U'
  if (user.name) return user.name.charAt(0)
  if (user.account) return user.account.charAt(0)
  return 'U'
} 